[{"timestamp": "2025-05-29T11:29:23.155334", "test_name": "SQL Injection - Login", "endpoint": "http://localhost:5000/api/auth/login", "method": "POST", "status_code": 401, "response": "{\n  \"error\": \"Invalid username or password\"\n}\n", "vulnerability_found": false, "details": "Payload: '; DROP TABLE users; --", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:29:25.205989", "test_name": "SQL Injection - Login", "endpoint": "http://localhost:5000/api/auth/login", "method": "POST", "status_code": 401, "response": "{\n  \"error\": \"Invalid username or password\"\n}\n", "vulnerability_found": false, "details": "Payload: ' OR '1'='1", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:29:27.246039", "test_name": "SQL Injection - Login", "endpoint": "http://localhost:5000/api/auth/login", "method": "POST", "status_code": 401, "response": "{\n  \"error\": \"Invalid username or password\"\n}\n", "vulnerability_found": false, "details": "Payload: ' UNION SELECT * FROM users --", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:29:29.281078", "test_name": "SQL Injection - Login", "endpoint": "http://localhost:5000/api/auth/login", "method": "POST", "status_code": 401, "response": "{\n  \"error\": \"Invalid username or password\"\n}\n", "vulnerability_found": false, "details": "Payload: admin'--", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:29:31.308514", "test_name": "SQL Injection - Login", "endpoint": "http://localhost:5000/api/auth/login", "method": "POST", "status_code": 401, "response": "{\n  \"error\": \"Invalid username or password\"\n}\n", "vulnerability_found": false, "details": "Payload: ' OR 1=1 --", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:29:33.559426", "test_name": "SQL Injection - User Registration", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 201, "response": "{\n  \"message\": \"User registered successfully\",\n  \"user\": {\n    \"course\": null,\n    \"created_at\": \"20", "vulnerability_found": true, "details": "Payload: '; DROP TABLE users; --", "security_status": "VULNERABLE"}, {"timestamp": "2025-05-29T11:29:35.797439", "test_name": "SQL Injection - User Registration", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 201, "response": "{\n  \"message\": \"User registered successfully\",\n  \"user\": {\n    \"course\": null,\n    \"created_at\": \"20", "vulnerability_found": true, "details": "Payload: ' OR '1'='1", "security_status": "VULNERABLE"}, {"timestamp": "2025-05-29T11:29:38.038165", "test_name": "XSS - User Registration (username)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 201, "response": "{\n  \"message\": \"User registered successfully\",\n  \"user\": {\n    \"course\": null,\n    \"created_at\": \"20", "vulnerability_found": true, "details": "Payload in username: <script>alert('XSS')</script>", "security_status": "VULNERABLE"}, {"timestamp": "2025-05-29T11:29:40.287544", "test_name": "XSS - User Registration (email)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 201, "response": "{\n  \"message\": \"User registered successfully\",\n  \"user\": {\n    \"course\": null,\n    \"created_at\": \"20", "vulnerability_found": true, "details": "Payload in email: <script>alert('XSS')</script>", "security_status": "VULNERABLE"}, {"timestamp": "2025-05-29T11:29:42.536967", "test_name": "XSS - User Registration (username)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 201, "response": "{\n  \"message\": \"User registered successfully\",\n  \"user\": {\n    \"course\": null,\n    \"created_at\": \"20", "vulnerability_found": true, "details": "Payload in username: javascript:alert('XSS')", "security_status": "VULNERABLE"}, {"timestamp": "2025-05-29T11:29:44.765890", "test_name": "XSS - User Registration (email)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 201, "response": "{\n  \"message\": \"User registered successfully\",\n  \"user\": {\n    \"course\": null,\n    \"created_at\": \"20", "vulnerability_found": true, "details": "Payload in email: javascript:alert('XSS')", "security_status": "VULNERABLE"}, {"timestamp": "2025-05-29T11:29:47.018369", "test_name": "XSS - User Registration (username)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 201, "response": "{\n  \"message\": \"User registered successfully\",\n  \"user\": {\n    \"course\": null,\n    \"created_at\": \"20", "vulnerability_found": true, "details": "Payload in username: <img src=x onerror=alert('XSS')>", "security_status": "VULNERABLE"}, {"timestamp": "2025-05-29T11:29:49.264557", "test_name": "XSS - User Registration (email)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 201, "response": "{\n  \"message\": \"User registered successfully\",\n  \"user\": {\n    \"course\": null,\n    \"created_at\": \"20", "vulnerability_found": true, "details": "Payload in email: <img src=x onerror=alert('XSS')>", "security_status": "VULNERABLE"}, {"timestamp": "2025-05-29T11:29:51.497895", "test_name": "XSS - User Registration (username)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 201, "response": "{\n  \"message\": \"User registered successfully\",\n  \"user\": {\n    \"course\": null,\n    \"created_at\": \"20", "vulnerability_found": true, "details": "Payload in username: ';alert('XSS');//", "security_status": "VULNERABLE"}, {"timestamp": "2025-05-29T11:29:53.763631", "test_name": "XSS - User Registration (email)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 201, "response": "{\n  \"message\": \"User registered successfully\",\n  \"user\": {\n    \"course\": null,\n    \"created_at\": \"20", "vulnerability_found": true, "details": "Payload in email: ';alert('XSS');//", "security_status": "VULNERABLE"}, {"timestamp": "2025-05-29T11:29:55.794446", "test_name": "Authentication Bypass", "endpoint": "http://localhost:5001/api/users/users", "method": "GET", "status_code": 401, "response": "{\"error\": \"Missing or invalid token\"}", "vulnerability_found": false, "details": "Attempted access without authentication token", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:29:57.814161", "test_name": "Authentication Bypass", "endpoint": "http://localhost:5002/api/students/students", "method": "GET", "status_code": 401, "response": "{\"error\": \"Missing or invalid token\"}", "vulnerability_found": false, "details": "Attempted access without authentication token", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:29:59.839523", "test_name": "Authentication Bypass", "endpoint": "http://localhost:5004/api/parents/parents", "method": "GET", "status_code": 401, "response": "{\"error\": \"Missing or invalid token\"}", "vulnerability_found": false, "details": "Attempted access without authentication token", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:30:01.868255", "test_name": "Authentication Bypass", "endpoint": "http://localhost:5003/api/courses/courses", "method": "POST", "status_code": 401, "response": "{\"error\": \"Missing or invalid token\"}", "vulnerability_found": false, "details": "Attempted access without authentication token", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:30:03.889123", "test_name": "Invalid <PERSON>", "endpoint": "http://localhost:5001/api/users/users", "method": "GET", "status_code": 401, "response": "{\"error\": \"Invalid token: Not enough segments\"}", "vulnerability_found": false, "details": "Attempted access with invalid token", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:30:20.764761", "test_name": "Data Integrity - First Parent Mapping", "endpoint": "http://localhost:5002/api/students/map-parent", "method": "POST", "status_code": 201, "response": "{\n  \"mapping\": {\n    \"created_at\": \"2025-05-29T06:00:20.757020\",\n    \"id\": 18,\n    \"parent_id\": 13,\n", "vulnerability_found": false, "details": "First parent mapping should succeed", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:30:24.824051", "test_name": "Data Integrity - Multiple Parents Constraint", "endpoint": "http://localhost:5002/api/students/map-parent", "method": "POST", "status_code": 400, "response": "{\n  \"error\": \"Student already has a parent mapped. Only one parent per student is allowed.\"\n}\n", "vulnerability_found": false, "details": "Second parent mapping should be rejected (one parent per student rule)", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:30:26.864158", "test_name": "CORS Configuration", "endpoint": "http://localhost:5000/api/auth/login", "method": "OPTIONS", "status_code": 200, "response": "{'Server': 'Werkzeug/3.0.4 Python/3.13.2', 'Date': 'Thu, 29 May 2025 06:00:26 GMT', 'Content-Type': 'text/html; charset=utf-8', 'Allow': 'OPTIONS, POST', 'Content-Length': '0', 'Connection': 'close'}", "vulnerability_found": false, "details": "Access-Control-Allow-Origin: ", "security_status": "SECURE"}]