2025-05-13 11:28:36,905 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:29:01,259 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:29:31,785 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:33:22,763 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:33:28,312 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:30,953 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:32,074 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:32,744 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:33,303 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:34,470 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:36,692 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:37:45,934 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:38:00,449 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:39:11,408 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:46:18,036 - student_service.common.middleware - WARNING - Invalid token for path: /register
2025-05-13 11:47:56,113 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:49:39,658 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:50:06,205 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/students
2025-05-13 11:54:27,411 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:54:46,435 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 15:18:07,343 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:23:19,473 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:31:40,464 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:32:52,209 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:35:51,485 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 15:38:42,118 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 15:45:02,805 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 15:47:43,746 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:48:29,302 - student_service.common.utils - ERROR - Error: Student with this user_id already exists, Status: 400
2025-05-13 15:50:31,583 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:35,682 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:37,733 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:50:41,834 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:45,932 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:47,978 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:50:52,066 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:52,940 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 15:50:56,173 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:58,226 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:51:57,330 - student_service.common.utils - ERROR - Error: Student with this user_id already exists, Status: 400
2025-05-13 15:52:03,476 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:52:45,863 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:53:25,274 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:54:02,160 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:54:48,392 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:50,431 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:52,470 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:54,520 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:56,564 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:58,610 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:00,667 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:02,715 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:04,763 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:06,816 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:08,863 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:10,910 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:27,541 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:29,592 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:31,642 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:33,689 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:35,742 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:37,794 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:39,847 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:41,904 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:43,954 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:46,008 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:48,054 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:50,090 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:57:37,761 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:58:21,993 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 16:02:08,495 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 16:03:38,502 - student_service.common.utils - ERROR - Error: Error mapping parent to student: (psycopg2.errors.ForeignKeyViolation) insert or update on table "parent_students" violates foreign key constraint "parent_students_student_id_fkey"
DETAIL:  Key (student_id)=(13) is not present in table "students".

[SQL: INSERT INTO parent_students (parent_id, student_id, relationship, created_at) VALUES (%(parent_id)s, %(student_id)s, %(relationship)s, %(created_at)s) RETURNING parent_students.id]
[parameters: {'parent_id': 14, 'student_id': 13, 'relationship': 'Mother', 'created_at': datetime.datetime(2025, 5, 13, 10, 33, 38, 492956)}]
(Background on this error at: https://sqlalche.me/e/14/gkpj), Status: 500
2025-05-13 18:21:12,527 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:12,790 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:12,838 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:29,467 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:30,082 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:31,226 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:31,532 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:31,843 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:32:21,467 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:32:21,733 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:32:21,779 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:32:29,230 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:32:29,843 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:33:58,337 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:33:58,602 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:34:35,750 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:35:00,823 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:35:18,098 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:35:18,362 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:35:46,017 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-27 15:43:48,456 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:44:32,061 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:44:55,064 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:49:21,994 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:51:02,108 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:52:56,896 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:53:05,002 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:54:53,517 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-27 15:58:22,429 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:59:37,929 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 16:08:54,389 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 16:09:04,175 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 16:09:09,948 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 16:11:37,878 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 16:15:33,464 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 16:19:41,003 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/student-parents/15
2025-05-27 16:19:43,049 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-27 16:22:31,691 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 16:26:47,326 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 18:38:33,456 - student_service.common.utils - ERROR - Error: Student with this user_id already exists, Status: 400
2025-05-27 18:38:49,222 - student_service.common.utils - ERROR - Error: Missing required fields: user_id, Status: 400
2025-05-27 18:39:02,791 - student_service.common.utils - ERROR - Error: Student with this user_id already exists, Status: 400
2025-05-28 13:05:03,667 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-28 13:05:03,989 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-28 13:05:14,654 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-28 13:05:14,663 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-28 13:05:45,662 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-28 13:05:45,670 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-28 13:05:52,655 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-28 13:05:52,662 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-29 11:06:34,743 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-29 11:07:00,202 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-29 11:10:56,586 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-29 11:11:17,607 - student_service.common.middleware - WARNING - Expired token for path: /api/students/map-parent
2025-05-29 11:12:38,166 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
